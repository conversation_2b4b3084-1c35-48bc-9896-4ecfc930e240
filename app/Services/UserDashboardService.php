<?php

namespace App\Services;

use App\Models\User;
use App\Traits\HelperTrait;

class UserDashboardService
{
    use HelperTrait;



    public function profile()
    {
        $user = User::select(
                'id',
                'uid',
                'name',
                'email',
                'phone',
                'avatar',
                'is_verified',
                'vendor_id',
                'tpl_id',
                'status'
            )->with('customer', 'addresses')->findOrFail(auth()->id());

        return $user;
    }

     public function updateProfile($request)
    {
        $user = auth()->user();
        $user->update($request->validated());
        return $user;
    }
}
