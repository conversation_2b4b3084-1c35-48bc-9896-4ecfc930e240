<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Str;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use <PERSON><PERSON><PERSON>Tokens, HasRoles, HasFactory;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'uid',
        'is_verified',
        'status',
        'is_active',
        'vendor_id',
        'two_factor_enabled',
    ];

       protected $casts = [
        'is_verified' => 'boolean',
        'is_active' => 'boolean',
        'two_factor_enabled' => 'boolean',
    ];

    const PENDING = 'pending';
    const ACTIVE = 'active';
    const INACTIVE = 'inactive';
    const BANNED = 'banned';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uid)) {
                $user->uid = (string) Str::uuid();
            }
        });
    }

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'avatar_url',
    ];

    protected function getDefaultGuardName(): string
    {
        return 'api';
    }

    public function customer()
    {
        return $this->hasOne(Customer::class);
    }

    public function getAvatarAttribute($value)
    {
        return $value;
    }

    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return config('filesystems.disks.s3.url') . '/' . $this->avatar;
        }
        return null;
    }

    public function vendor()
    {
        return $this->hasOne(Vendor::class, 'id', 'vendor_id');
    }

    public function addresses()
    {
        return $this->hasMany(UserAddress::class, 'user_id', 'id');
    }

    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Find user for Passport authentication
     * This allows login with either email or phone
     */
    public function findForPassport($username)
    {
        // Check if username is an email
        if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
            return $this->where('email', $username)->first();
        }

        // Otherwise treat as phone number
        return $this->where('phone', $username)->first();
    }
}
