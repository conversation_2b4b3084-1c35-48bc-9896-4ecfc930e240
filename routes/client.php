<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Client\BrandController;
use App\Http\Controllers\Client\CartController;
use App\Http\Controllers\Client\CustomerController;
use App\Http\Controllers\Client\HomeController;
use App\Http\Controllers\Client\OrderController;
use App\Http\Controllers\Client\UserAddressController;
use App\Http\Controllers\Client\UserCartController;
use App\Http\Controllers\Client\WishlistController;
use App\Http\Controllers\Client\ReviewController;
use App\Http\Controllers\Client\SettingController;
use App\Http\Controllers\Client\CategoryInformationController;
use App\Http\Controllers\Client\ClassInformationController;
use App\Http\Controllers\Client\FiltersController;
use App\Http\Controllers\Client\UniversalProductsController;
use App\Http\Controllers\CommonController;
use App\Http\Controllers\PopupController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\UserDashboardController;
use Illuminate\Support\Facades\Route;
use SebastianBergmann\CodeCoverage\Report\Html\Dashboard;

Route::middleware('auth:api')->group(function () {
    Route::get('profile', [UserDashboardController::class, 'profile']);
    Route::post('update-profile', [UserDashboardController::class, 'updateProfile']);
    Route::post('change-password', [AuthController::class, 'changePassword']);
    Route::apiResource('user-addresses', UserAddressController::class);

    // Order management routes
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('/summary', [OrderController::class, 'summary']);
        Route::get('/analytics', [OrderController::class, 'analytics']);
        Route::post('/create-from-cart', [OrderController::class, 'createFromCart']);
        Route::post('/convert-cart', [OrderController::class, 'convertCart']);
        Route::get('/{uuid}', [OrderController::class, 'show']);
        Route::patch('/{uuid}/cancel', [OrderController::class, 'cancel']);
    });
});

Route::get('menu', [CommonController::class, 'menu']);
Route::get('/', [HomeController::class, 'index']);
//single product 
Route::get('products/{product_uuid}', [ProductController::class, 'getProductInformation']);

Route::get('categories', [CategoryInformationController::class, 'getAllCategories']);
Route::get('categories/{parent_id}/subcategories', [CategoryInformationController::class, 'getSubcategoriesByParent']);

Route::get('categories/{category_slug}', [CategoryInformationController::class, 'getCategoryInformation']);
Route::get('subcategories/{subcategory_slug}', [CategoryInformationController::class, 'getSubcategoryInformation']);
Route::get('product-classes/{class_slug}', [ClassInformationController::class, 'getClassInformation']);
Route::get('product-subclasses/{class_slug}', [ClassInformationController::class, 'getSubclassesInformation']);
Route::get('brands/{brand_slug}', [BrandController::class, 'brandInformation']);

Route::get('filters', [FiltersController::class, 'getFilters']);
Route::get('products', [UniversalProductsController::class, 'getProducts']);
Route::get('brands', [BrandController::class, 'getGroupedBrands']);

// Cart routes with session middleware for guest cart management
Route::middleware(['cart.session'])->prefix('cart')->group(function () {
    // Guest cart operations
    Route::post('/', [CartController::class, 'createCart']);
    Route::get('/{cartId}', [CartController::class, 'getCart']);
    Route::delete('/{cartId}', [CartController::class, 'clearCart']);

    // Cart item operations
    Route::post('/{cartId}/items', [CartController::class, 'addItem']);
    Route::put('/{cartId}/items/{itemId}', [CartController::class, 'updateItem']);
    Route::delete('/{cartId}/items/{itemId}', [CartController::class, 'removeItem']);
    Route::post('/{cartId}/items/bulk', [CartController::class, 'bulkUpdateItems']);

    // Cart calculations and coupons
    Route::post('/{cartId}/apply-coupon', [CartController::class, 'applyCoupon']);
    Route::delete('/{cartId}/remove-coupon', [CartController::class, 'removeCoupon']);

    // Multi-vendor operations
    Route::get('/{cartId}/vendors', [CartController::class, 'getVendorSplit']);
    Route::post('/{cartId}/merge', [CartController::class, 'mergeCarts']);

    // Cart validation
    Route::post('/{cartId}/validate', [CartController::class, 'validateCart']);
});

// Authenticated user cart operations
Route::middleware(['auth:api', 'cart.session'])->prefix('my-cart')->group(function () {
    Route::get('/', [UserCartController::class, 'getCurrentCart']);
    Route::post('/migrate', [UserCartController::class, 'migrateGuestCart']);
    Route::get('/history', [UserCartController::class, 'getCartHistory']);
    Route::post('/save-for-later', [UserCartController::class, 'saveForLater']);
    Route::get('/saved-items', [UserCartController::class, 'getSavedItems']);
    Route::get('/statistics', [UserCartController::class, 'getCartStatistics']);
    Route::delete('/clear', [UserCartController::class, 'clearCurrentCart']);
});

// Wishlist operations
Route::middleware(['auth:api'])->prefix('wishlist')->group(function () {
    Route::get('/', [WishlistController::class, 'index']);
    Route::post('/', [WishlistController::class, 'store']);
    Route::get('/{id}', [WishlistController::class, 'show']);
    Route::delete('/{id}', [WishlistController::class, 'destroy']);
    Route::post('/{id}/move-to-cart', [WishlistController::class, 'moveToCart']);
    Route::post('/bulk-move-to-cart', [WishlistController::class, 'bulkMoveToCart']);
    Route::post('/bulk-delete', [WishlistController::class, 'bulkDelete']);
});

// Review operations
Route::middleware(['auth:api'])->prefix('reviews')->group(function () {
    Route::get('/', [ReviewController::class, 'index']);
    Route::post('/', [ReviewController::class, 'store']);
    Route::get('/{id}', [ReviewController::class, 'show']);
    Route::put('/{id}', [ReviewController::class, 'update']);
    Route::delete('/{id}', [ReviewController::class, 'destroy']);
});

// Public review endpoints (no authentication required)
Route::prefix('reviews')->group(function () {
    Route::get('/product/{productId}', [ReviewController::class, 'getProductReviews']);
    Route::get('/vendor/{vendorId}', [ReviewController::class, 'getVendorReviews']);
    Route::get('/summary', [ReviewController::class, 'getReviewSummary']);
});

Route::get('popups/{type}', [PopupController::class, 'getPopupsByType']);

// Blog routes (public access)
Route::prefix('blogs')->group(function () {
    Route::get('/', [\App\Http\Controllers\Client\BlogController::class, 'index']);
    Route::get('/featured', [\App\Http\Controllers\Client\BlogController::class, 'getFeatured']);
    Route::get('/search', [\App\Http\Controllers\Client\BlogController::class, 'search']);
    Route::get('/category/{categorySlug}', [\App\Http\Controllers\Client\BlogController::class, 'getByCategory']);
    Route::get('/{blogId}/comments', [\App\Http\Controllers\Client\BlogCommentController::class, 'index']);
    Route::get('/{slug}', [\App\Http\Controllers\Client\BlogController::class, 'show']);
    Route::get('/{slug}/related', [\App\Http\Controllers\Client\BlogController::class, 'getRelated']);
});

// Blog comment routes (public access for viewing)
Route::prefix('comments')->group(function () {
    Route::get('/{id}', [\App\Http\Controllers\Client\BlogCommentController::class, 'show']);
});

// Blog comment routes (authenticated)
Route::middleware('auth:api')->prefix('comments')->group(function () {
    Route::get('/my-comments', [\App\Http\Controllers\Client\BlogCommentController::class, 'myComments']);
    Route::post('/', [\App\Http\Controllers\Client\BlogCommentController::class, 'store']);
    Route::post('/reply', [\App\Http\Controllers\Client\BlogCommentController::class, 'reply']);
    Route::put('/{id}', [\App\Http\Controllers\Client\BlogCommentController::class, 'update']);
    Route::delete('/{id}', [\App\Http\Controllers\Client\BlogCommentController::class, 'destroy']);
});

// Public Settings Routes (no authentication required)
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/public', [SettingController::class, 'getPublicSettings'])->name('public');
    Route::get('/category/{category}/public', [SettingController::class, 'getPublicByCategory'])->name('category.public');
});
