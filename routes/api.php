<?php

use App\Http\Controllers\Client\CustomerController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PopupController;
use App\Http\Controllers\OfferAndDealController;
use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Admin\BannerItemController;
use App\Http\Controllers\Admin\BlogCategoryController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\CouponController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\ReviewModerationController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\CommonController;
use App\Http\Controllers\VendorEoiController;
use App\Http\Controllers\SupportCategoryController;
use App\Http\Controllers\SupportTopicController;
use App\Http\Controllers\Admin\DevDatabaseManagerController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\SupportTicketMessageController;
use App\Http\Controllers\ProductClassController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\ProductVariantController;
use App\Http\Controllers\ProductAttributeController;
use App\Http\Controllers\ProductMediaController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\SupportReasonController;
use App\Http\Controllers\FulfilmentController;
use App\Http\Controllers\DropdownController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\Admin\EmailTemplateCategoryController;
use App\Http\Controllers\Admin\EmailTemplateController;
use App\Http\Controllers\Admin\EmailTemplateVariableController;
use App\Http\Controllers\Admin\SettingController;


Route::middleware('auth:api')->group(function () {
    Route::post('file-upload', [CommonController::class, 'fileUpload']);
    Route::post('multiple-file-upload', [CommonController::class, 'multipleFileUpload']);
    Route::prefix('admin')->middleware('role:admin')->group(function () {
        Route::apiResource('popups', PopupController::class);
        Route::apiResource('offer-and-deals', OfferAndDealController::class);
        Route::apiResource('inventories', InventoryController::class);
        Route::apiResource('product-variants', ProductVariantController::class);
        Route::apiResource('product-attributes', ProductAttributeController::class);
        Route::apiResource('product-media', ProductMediaController::class);
        Route::apiResource('products', ProductController::class);
        Route::apiResource('warehouses', WarehouseController::class);
        Route::apiResource('support-categories', SupportCategoryController::class);
        Route::apiResource('support-topics', SupportTopicController::class);
        Route::apiResource('support-reasons', SupportReasonController::class);
        Route::apiResource('support-tickets', SupportTicketController::class);
        Route::apiResource('fulfilments', FulfilmentController::class);
        Route::apiResource('support-ticket-messages', SupportTicketMessageController::class);
        Route::get('support-tickets/{ticketId}/messages', [SupportTicketMessageController::class, 'getThreadedMessages']);
        Route::apiResource('banners', BannerController::class);
        Route::apiResource('banner-items', BannerItemController::class);
        Route::apiResource('customers', CustomerController::class);
        Route::apiResource('brands', BrandController::class);
        Route::apiResource('categories', CategoryController::class);
        Route::apiResource('product-classes', ProductClassController::class);
        Route::apiResource('blogs', BlogController::class);
        Route::apiResource('blog-categories', BlogCategoryController::class);
        // Additional coupon routes (must be before apiResource to avoid conflicts)
        Route::prefix('coupons')->group(function () {
            Route::get('vendor/{vendorId}', [CouponController::class, 'vendorCoupons']);
            Route::get('platform/all', [CouponController::class, 'platformCoupons']);
            Route::post('validate', [CouponController::class, 'validateCode']);
            Route::get('statistics', [CouponController::class, 'stats']);
            Route::patch('{id}/toggle-status', [CouponController::class, 'toggleStatus']);
        });

        Route::apiResource('coupons', CouponController::class);

        // Order management routes
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index']);
            Route::post('/', [OrderController::class, 'store']);
            Route::get('/dashboard', [OrderController::class, 'dashboard']);
            Route::get('/analytics', [OrderController::class, 'analytics']);
            Route::patch('/bulk-status', [OrderController::class, 'bulkUpdateStatus']);
            Route::get('/{uuid}', [OrderController::class, 'show']);
            Route::put('/{uuid}', [OrderController::class, 'update']);
            Route::patch('/{uuid}/status', [OrderController::class, 'updateStatus']);
            Route::patch('/{uuid}/cancel', [OrderController::class, 'cancel']);
        });

        Route::apiResource('pages', PageController::class);
        Route::apiResource('dropdowns', DropdownController::class);
        Route::apiResource('permissions', PermissionController::class);
        Route::apiResource('roles', RoleController::class);
        Route::apiResource('users', UserController::class);
        Route::apiResource('vendors', VendorController::class);
        Route::apiResource('vendor-eoi', VendorEoiController::class);

        Route::get('permissions-list', [PermissionController::class, 'permissionList']);
        Route::post('product-media/store-update', [ProductMediaController::class, 'storeUpdate']);
        Route::put('vendors/{vendor}/approve', [VendorController::class, 'approveVendor']);
        Route::put('vendors/{vendor}/active-deactivate', [VendorController::class, 'activeDeactivateVendor']);
        Route::post('vendor-eoi/approve', [VendorEoiController::class, 'approveEOI']);

        // Email Template Management Routes
        Route::prefix('email-templates')->name('admin.email-templates.')->group(function () {
            // Template Categories
            Route::get('categories', [EmailTemplateCategoryController::class, 'index'])->name('categories.index');
            Route::post('categories', [EmailTemplateCategoryController::class, 'store'])->name('categories.store');
            Route::get('categories/{slug}', [EmailTemplateCategoryController::class, 'show'])->name('categories.show');
            Route::put('categories/{slug}', [EmailTemplateCategoryController::class, 'update'])->name('categories.update');
            Route::delete('categories/{slug}', [EmailTemplateCategoryController::class, 'destroy'])->name('categories.destroy');

            // Template Variables
            Route::get('variables', [EmailTemplateVariableController::class, 'index'])->name('variables.index');
            Route::post('variables', [EmailTemplateVariableController::class, 'store'])->name('variables.store');
            Route::get('variables/{id}', [EmailTemplateVariableController::class, 'show'])->name('variables.show');
            Route::put('variables/{id}', [EmailTemplateVariableController::class, 'update'])->name('variables.update');
            Route::delete('variables/{id}', [EmailTemplateVariableController::class, 'destroy'])->name('variables.destroy');

            // Email Templates
            Route::get('templates', [EmailTemplateController::class, 'index'])->name('templates.index');
            Route::post('templates', [EmailTemplateController::class, 'store'])->name('templates.store');
            Route::get('templates/{uuid}', [EmailTemplateController::class, 'show'])->name('templates.show');
            Route::put('templates/{uuid}', [EmailTemplateController::class, 'update'])->name('templates.update');
            Route::delete('templates/{uuid}', [EmailTemplateController::class, 'destroy'])->name('templates.destroy');

            // Template Preview and Testing
            Route::post('templates/{uuid}/preview', [EmailTemplateController::class, 'preview'])->name('templates.preview');
            Route::post('templates/{uuid}/test', [EmailTemplateController::class, 'sendTest'])->name('templates.test');
            Route::post('templates/{uuid}/validate', [EmailTemplateController::class, 'validate'])->name('templates.validate');

            // Template History and Versioning
            Route::get('templates/{uuid}/history', [EmailTemplateController::class, 'history'])->name('templates.history');
            Route::get('templates/{uuid}/history/{version}', [EmailTemplateController::class, 'showVersion'])->name('templates.history.show');
            Route::post('templates/{uuid}/history/{version}/restore', [EmailTemplateController::class, 'restoreVersion'])->name('templates.history.restore');
        });

        // Review Moderation Routes
        Route::prefix('reviews')->group(function () {
            Route::get('/', [ReviewModerationController::class, 'index']);
            Route::get('/{id}', [ReviewModerationController::class, 'show']);
            Route::post('/{id}/approve', [ReviewModerationController::class, 'approve']);
            Route::post('/{id}/reject', [ReviewModerationController::class, 'reject']);
            Route::post('/{id}/hide', [ReviewModerationController::class, 'hide']);
            Route::post('/{id}/show', [ReviewModerationController::class, 'showReview']);
            Route::delete('/{id}', [ReviewModerationController::class, 'destroy']);
            Route::post('/bulk-approve', [ReviewModerationController::class, 'bulkApprove']);
            Route::post('/bulk-reject', [ReviewModerationController::class, 'bulkReject']);
            Route::post('/bulk-hide', [ReviewModerationController::class, 'bulkHide']);
            Route::post('/bulk-show', [ReviewModerationController::class, 'bulkShow']);
            Route::post('/bulk-delete', [ReviewModerationController::class, 'bulkDelete']);
        });

        // Settings Management Routes
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingController::class, 'index'])->name('index');
            Route::post('/', [SettingController::class, 'store'])->name('store');
            Route::get('/category/{category}', [SettingController::class, 'getByCategory'])->name('category');
            Route::get('/active-list', [SettingController::class, 'activeList'])->name('active-list');
            Route::post('/bulk-update', [SettingController::class, 'bulkUpdate'])->name('bulk-update');
            Route::get('/{key}', [SettingController::class, 'show'])->name('show');
            Route::put('/{key}', [SettingController::class, 'update'])->name('update');
            Route::delete('/{key}', [SettingController::class, 'destroy'])->name('destroy');
        });

    });
    Route::prefix('general')->group(function () {
        Route::get('warehouses/active-list', [WarehouseController::class, 'warehouseListByActive']);
        Route::get('vendors/active-list', [VendorController::class, 'vendorListByActive']);
        Route::get('product-list-by-vendor/{vendor}', [ProductClassController::class, 'productListByVendor']);
    });
});


Route::prefix('general')->group(function () {
    // Consolidated dropdown data endpoint
    Route::get('dropdown-data', [CommonController::class, 'getConsolidatedDropdownData']);

    // Individual dropdown endpoints (for specific use cases)
    Route::get('blog-categories/active-list', [BlogCategoryController::class, 'categoryListByActive']);
    Route::get('categories/active-list', [CategoryController::class, 'categoryListByActive']);
    Route::get('sub-categories/by-category/{category}', [CategoryController::class, 'subCategoryList']);
    Route::put('categories/status-update/{category}', [CategoryController::class, 'statusUpdate']);
    Route::get('classes/by-category/{category}', [ProductClassController::class, 'getClassByCategory']);
    Route::get('classes/active-list', [ProductClassController::class, 'classListByActive']);
    Route::get('sub-classes/by-class/{class}', [ProductClassController::class, 'getSubClasses']);
    Route::put('classes/status-update/{class}', [ProductClassController::class, 'statusUpdate']);
    Route::get('brands/active-list', [BrandController::class, 'brandListByActive']);
    Route::put('brands/status-update/{brand}', [BrandController::class, 'status']);
    Route::put('users/ban-unban/{user}', [UserController::class, 'userBanUnBan']);
    Route::put('warehouses/status-update/{warehouse}', [WarehouseController::class, 'statusUpdate']);
    Route::get('dropdowns/active-list', [DropdownController::class, 'dropdownListByActive']);
    Route::get('support-categories/active-list', [SupportCategoryController::class, 'activeList']);
    Route::get('support-topics/active-list', [SupportTopicController::class, 'activeList']);
    Route::get('support-reasons/active-list', [SupportReasonController::class, 'activeList']);
    Route::get('fulfilments/active-list', [FulfilmentController::class, 'activeList']);
    Route::post('validate', [CouponController::class, 'validateCode']);
    Route::get('product-attributes/active-list', [ProductAttributeController::class, 'activeList']);
    Route::get('banners/active-list', [BannerController::class, 'activeList']);
    
});

Route::get('categories/active-list', [CategoryController::class, 'categoryListByActive']);
Route::get('brands/active-list', [BrandController::class, 'brandListByActive']);

/**
 * Development Database Manager Routes (No CSRF Protection)
 * Only accessible in development environment
 */
Route::prefix('dev')->group(function () {
    Route::get('test', [DevDatabaseManagerController::class, 'test']);
    Route::get('system-info', [DevDatabaseManagerController::class, 'systemInfo']);
    Route::post('execute-command', [DevDatabaseManagerController::class, 'executeCommand']);
    Route::post('quick-refresh', [DevDatabaseManagerController::class, 'quickRefresh']);
});


